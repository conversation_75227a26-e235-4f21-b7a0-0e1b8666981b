:root {
    /* ------- Enhanced Base Colors for WCAG Compliance ------- */
    --primary-color: #1f1f23; /* Darker for better contrast (4.5:1 ratio) */
    --secondary-color: #52525b; /* Enhanced contrast while maintaining monochromatic scheme */
    --tertiary-color: #a1a1aa; /* Improved readability */
    --quaternary-color: #e4e4e7;
    --link-color: #71717a;
    --accent-color: #f4f4f5;

    /* ------- Enhanced Glassmorphism Colors for Professional Appearance ------- */
    --glass-base: rgba(248, 250, 252, 0.28); /* Slightly increased opacity for better visibility */
    --glass-hover: rgba(248, 250, 252, 0.38); /* Enhanced hover state */
    --glass-active: rgba(248, 250, 252, 0.48); /* More pronounced active state */
    --glass-border: rgba(255, 255, 255, 0.35); /* Improved border visibility */
    --glass-border-hover: rgba(255, 255, 255, 0.45); /* Enhanced hover border */

    /* ------- Progress Bar Colors ------- */
    --progress-primary: #f4f4f5;
    --progress-secondary: #d4d4d8;
    --progress-tertiary: #a1a1aa;
    --progress-quaternary: #71717a;
    --progress-glow: rgba(244, 244, 245, 0.6);
    --progress-glow-secondary: rgba(212, 212, 216, 0.4);

    /* ------- Interactive States ------- */
    --hover-overlay: rgba(255, 255, 255, 0.15);
    --active-overlay: rgba(255, 255, 255, 0.25);
    --focus-ring: rgba(244, 244, 245, 0.6);

    /* ------- Enhanced Icon Colors for Better Accessibility ------- */
    --icon-default: #6b7280; /* Improved contrast while maintaining monochromatic scheme */
    --icon-hover: #1f2937; /* Enhanced hover state with better contrast */
    --icon-active: #4b5563; /* More pronounced active state */

    /* ------- Additional Accent Colors ------- */
    --success-color: #d4d4d8;
    --warning-color: #a1a1aa;
    --error-color: #71717a;
    --info-color: #e4e4e7;

    /* ------- Dark Grey/Silver Mode Colors for Sophisticated Appearance ------- */
    --dark-glass-base: rgba(55, 65, 81, 0.7); /* Dark grey with good opacity */
    --dark-glass-hover: rgba(75, 85, 99, 0.8); /* Medium grey hover */
    --dark-glass-active: rgba(107, 114, 128, 0.9); /* Light grey active state */
    --dark-glass-border: rgba(156, 163, 175, 0.4); /* Silver border */
    --dark-glass-border-hover: rgba(209, 213, 219, 0.6); /* Light silver hover border */
    --dark-hover-overlay: rgba(255, 255, 255, 0.1); /* Subtle white overlay */
    --dark-active-overlay: rgba(255, 255, 255, 0.18); /* Enhanced white overlay */

    /* ------- Dark Grey/Silver Icon Colors for Better Accessibility ------- */
    --dark-icon-default: #d1d5db; /* Light grey for good visibility */
    --dark-icon-hover: #f9fafb; /* Near white hover state */
    --dark-icon-active: #9ca3af; /* Medium grey active state */

    /* ------- Dark Grey/Silver Text Colors for WCAG Compliance ------- */
    --dark-primary-text: #f9fafb; /* Near white for maximum contrast */
    --dark-secondary-text: #e5e7eb; /* Light grey secondary text */
    --dark-tertiary-text: #d1d5db; /* Medium light grey tertiary text */
    --dark-muted-text: #9ca3af; /* Medium grey muted text */
    /* ----------------------------------- */

    /* ------- fonts ------- */
    --primary-font: "Poppins", sans-serif;
    --secondary-font: "Montserrat", sans-serif;
    --heading-weight: 600;
    --body-weight: 400;
    --heading-line-height: 1.2;
    --body-line-height: 1.6;
    /* --------------------- */

    /* ------- spacing ------- */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    /* ----------------------- */

    /* ------- transitions ------- */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* ------- theme switching transitions ------- */
    --theme-transition-duration: 0.6s;
    --theme-transition-timing: cubic-bezier(0.4, 0, 0.2, 1);
    --theme-transition-background: background 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    --theme-transition-color: color 0.4s ease;
    --theme-transition-all: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    /* ------------------------------------------- */
}
